{"name": "upgrade-admin", "version": "1.0.0-build.17", "scripts": {"dev": "vite --mode development", "dev:bfbtest": "vite --mode bfbtest", "dev:pro": "vite --mode development", "build": "vite build --mode development", "build:fazhi": "vite build --mode fazhi", "build:bfbtest": "vite build --mode bfbtest", "build:bfbprod": "vite build --mode bfbprod", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --noEmit", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage"}, "dependencies": {"@babel/core": "^7.20.12", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.20.7", "@headlessui/vue": "^1.7.16", "@heroicons/vue": "^2.0.18", "@originjs/vite-plugin-commonjs": "^1.0.3", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-commonjs": "^24.0.1", "@tailwindcss/forms": "^0.5.7", "@types/js-md5": "^0.7.0", "@types/lodash-es": "^4.17.6", "@types/qrcode": "^1.5.0", "@types/tus-js-client": "^1.8.0", "@vueuse/components": "^9.13.0", "@vueuse/core": "^9.4.0", "axios": "^0.22.0", "crypto": "^1.0.1", "dayjs": "^1.11.7", "js-md5": "^0.7.3", "lodash-es": "^4.17.21", "lucide-vue-next": "^0.516.0", "luxon": "^3.0.4", "mitt": "^3.0.0", "nanoid": "^4.0.0", "pinia": "^2.0.23", "qs": "^6.12.3", "sass": "^1.55.0", "tailwindcss": "^3.3.6", "tus-js-client": "^4.3.1", "vue": "^3.2.41", "vue-i18n": "^9.2.2", "vue-router": "^4.1.5"}, "devDependencies": {"@arco-design/web-vue": "^2.51.0", "@rushstack/eslint-patch": "^1.1.4", "@types/jest": "^29.4.4", "@types/node": "^16.11.68", "@vitejs/plugin-vue": "^3.1.2", "@vitest/ui": "^3.2.4", "@vue/eslint-config-prettier": "^7.0.0", "@vue/eslint-config-typescript": "^11.0.0", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.1.3", "autoprefixer": "^10.4.16", "babel-jest": "^29.5.0", "babel-loader": "^8.2.2", "babel-preset-env": "^1.7.0", "c8": "^10.1.3", "copy-webpack-plugin": "^11.0.0", "cross-env": "^7.0.3", "eslint": "^8.22.0", "eslint-plugin-vue": "^9.3.0", "jest": "^29.5.0", "jest-localstorage-mock": "^2.4.26", "jsdom": "^26.1.0", "json5": "2.2.1", "less": "^4.1.3", "npm-run-all": "^4.1.5", "postcss": "^8.4.32", "prettier": "^2.7.1", "rollup-plugin-copy": "^3.4.0", "rollup-plugin-scss": "^4.0.0", "terser-webpack-plugin": "^5.3.7", "ts-jest": "^29.0.5", "ts-node": "^10.9.1", "typescript": "~4.7.4", "vite": "^4.1.4", "vite-plugin-pages": "^0.33.0", "vite-plugin-static-copy": "^0.13.1", "vitest": "^3.2.4", "vue-tsc": "^1.0.8", "webpack": "^5.76.1", "webpack-cli": "^5.0.1"}, "private": true}