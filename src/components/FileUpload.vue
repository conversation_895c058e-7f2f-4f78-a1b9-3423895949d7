<template>
  <div class="file-upload-container">
    <a-upload
      :custom-request="handleUpload"
      :show-file-list="false"
      :accept="accept"
      :disabled="uploading"
      @before-upload="beforeUpload"
    >
      <template #upload-button>
        <div
          :class="[
            'upload-area',
            'border-2 border-dashed rounded-lg p-6 text-center transition-all duration-200',
            uploading ? 'border-blue-300 bg-blue-50' : 'border-gray-300 hover:border-blue-400 hover:bg-gray-50',
            isDark && !uploading ? 'border-gray-600 hover:border-blue-500 hover:bg-gray-800/50' : '',
            isDark && uploading ? 'border-blue-400 bg-blue-900/20' : ''
          ]"
        >
          <div v-if="!uploading" class="upload-content">
            <div :class="['upload-icon mb-3', isDark ? 'text-gray-400' : 'text-gray-500']">
              <Upload class="h-8 w-8 mx-auto" />
            </div>
            <div :class="['upload-text', isDark ? 'text-gray-300' : 'text-gray-700']">
              <p class="text-sm font-medium">点击上传文件</p>
              <p class="text-xs mt-1">或将文件拖拽到此处</p>
            </div>
            <div v-if="accept" :class="['upload-hint text-xs mt-2', isDark ? 'text-gray-500' : 'text-gray-400']">
              支持格式: {{ acceptText }}
            </div>
            <div v-if="maxSize" :class="['upload-hint text-xs', isDark ? 'text-gray-500' : 'text-gray-400']">
              最大文件大小: {{ formatFileSize(maxSize) }}
            </div>
          </div>
          
          <div v-else class="upload-progress">
            <div :class="['upload-icon mb-3', isDark ? 'text-blue-400' : 'text-blue-500']">
              <Loader2 v-if="!isPaused" class="h-8 w-8 mx-auto animate-spin" />
              <Upload v-else class="h-8 w-8 mx-auto" />
            </div>
            <div :class="['upload-text', isDark ? 'text-gray-300' : 'text-gray-700']">
              <p class="text-sm font-medium">{{ isPaused ? '上传已暂停' : '正在上传...' }}</p>
              <div class="mt-2">
                <a-progress
                  :percent="uploadProgress"
                  :show-text="false"
                  size="small"
                  :color="isDark ? '#3b82f6' : undefined"
                />
                <div class="flex items-center justify-between mt-1">
                  <p class="text-xs">{{ uploadProgress }}%</p>
                  <div class="flex space-x-2">
                    <a-button
                      v-if="!isPaused"
                      size="mini"
                      type="text"
                      @click="pauseUpload"
                    >
                      暂停
                    </a-button>
                    <a-button
                      v-else
                      size="mini"
                      type="primary"
                      @click="resumeUpload"
                    >
                      继续
                    </a-button>
                    <a-button
                      size="mini"
                      type="text"
                      status="danger"
                      @click="cancelUpload"
                    >
                      取消
                    </a-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </a-upload>
    
    <!-- 已上传文件显示 -->
    <div v-if="fileInfo && !uploading" class="uploaded-file mt-3">
      <div
        :class="[
          'flex items-center justify-between p-3 rounded-lg border',
          isDark ? 'border-gray-600 bg-gray-800/50' : 'border-gray-200 bg-gray-50'
        ]"
      >
        <div class="flex items-center space-x-3">
          <div :class="['file-icon', isDark ? 'text-green-400' : 'text-green-500']">
            <FileCheck class="h-5 w-5" />
          </div>
          <div>
            <p :class="['text-sm font-medium', isDark ? 'text-gray-200' : 'text-gray-900']">
              {{ fileInfo.name }}
            </p>
            <p :class="['text-xs', isDark ? 'text-gray-400' : 'text-gray-500']">
              {{ formatFileSize(fileInfo.size) }}
            </p>
          </div>
        </div>
        <div class="flex items-center space-x-2">
          <a-button
            size="small"
            type="text"
            :class="isDark ? 'text-gray-400 hover:text-gray-200' : 'text-gray-500 hover:text-gray-700'"
            @click="clearFile"
          >
            <template #icon>
              <X class="h-4 w-4" />
            </template>
          </a-button>
        </div>
      </div>
    </div>
    
    <!-- 错误信息 -->
    <div v-if="errorMessage" class="error-message mt-2">
      <p :class="['text-sm', isDark ? 'text-red-400' : 'text-red-600']">
        {{ errorMessage }}
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Upload, Loader2, FileCheck, X } from 'lucide-vue-next'
import { useThemeStore } from '@/stores/themeStore'
import { uploadFile, validateFileType, validateFileSize, formatFileSize, TusUploadManager } from '@/utils/fileUpload'
import type { FileUploadOptions, UploadResult } from '@/utils/fileUpload'
import { Message } from '@arco-design/web-vue'

interface Props {
  accept?: string
  maxSize?: number // 字节
  modelValue?: string // 下载地址
}

interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'upload-success', result: UploadResult): void
  (e: 'upload-error', error: Error): void
}

const props = withDefaults(defineProps<Props>(), {
  accept: '',
  maxSize: 100 * 1024 * 1024 // 默认100MB
})

const emit = defineEmits<Emits>()

const themeStore = useThemeStore()
const isDark = computed(() => themeStore.isDark)

const uploading = ref(false)
const uploadProgress = ref(0)
const fileInfo = ref<{ name: string; size: number } | null>(null)
const errorMessage = ref('')
const isPaused = ref(false)
const uploadManager = ref<TusUploadManager | null>(null)

const acceptText = computed(() => {
  if (!props.accept) return '所有文件'
  const types = props.accept.split(',').map(type => type.trim())
  // 将文件扩展名转换为更友好的显示格式
  const friendlyTypes = types.map(type => {
    if (type.startsWith('.')) {
      return type.toUpperCase() // .apk -> APK
    }
    return type
  })
  return friendlyTypes.join(', ')
})

const beforeUpload = (file: File) => {
  errorMessage.value = ''
  
  // 验证文件类型
  if (props.accept) {
    const allowedTypes = props.accept.split(',').map(type => type.trim())
    if (!validateFileType(file, allowedTypes)) {
      errorMessage.value = `不支持的文件类型，请选择 ${acceptText.value} 格式的文件`
      return false
    }
  }
  
  // 验证文件大小
  if (props.maxSize && !validateFileSize(file, props.maxSize)) {
    errorMessage.value = `文件大小超过限制，最大允许 ${formatFileSize(props.maxSize)}`
    return false
  }
  
  return true
}

const handleUpload = async (option: any) => {
  const { file } = option
  
  uploading.value = true
  uploadProgress.value = 0
  errorMessage.value = ''
  
  try {
    const uploadOptions: FileUploadOptions = {
      fileName: file.name,
      mimeType: file.type,
      onProgress: (progress) => {
        uploadProgress.value = progress
      },
      onComplete: (fileId) => {
        console.log('Upload complete, fileId:', fileId)
      },
      onError: (error) => {
        console.error('Upload error:', error)
      }
    }
    
    const result = await uploadFile(file, uploadOptions)
    
    // 保存文件信息
    fileInfo.value = {
      name: file.name,
      size: file.size
    }
    
    // 更新下载地址
    emit('update:modelValue', result.downloadUrl)
    emit('upload-success', result)
    
    Message.success('文件上传成功')
  } catch (error) {
    console.error('Upload failed:', error)
    errorMessage.value = error instanceof Error ? error.message : '上传失败'
    emit('upload-error', error as Error)
    Message.error('文件上传失败')
  } finally {
    uploading.value = false
    uploadProgress.value = 0
  }
}

const clearFile = () => {
  fileInfo.value = null
  errorMessage.value = ''
  isPaused.value = false
  uploadManager.value = null
  emit('update:modelValue', '')
}

const pauseUpload = () => {
  if (uploadManager.value) {
    uploadManager.value.pause()
    isPaused.value = true
  }
}

const resumeUpload = () => {
  if (uploadManager.value) {
    uploadManager.value.resume()
    isPaused.value = false
  }
}

const cancelUpload = () => {
  if (uploadManager.value) {
    uploadManager.value.cancel()
    uploading.value = false
    uploadProgress.value = 0
    isPaused.value = false
    uploadManager.value = null
    errorMessage.value = '上传已取消'
  }
}
</script>

<style scoped lang="less">
.file-upload-container {
  .upload-area {
    cursor: pointer;
    
    &:hover {
      transform: translateY(-1px);
    }
  }
  
  .upload-content,
  .upload-progress {
    pointer-events: none;
  }
  
  .uploaded-file {
    .file-icon {
      flex-shrink: 0;
    }
  }
}
</style>
