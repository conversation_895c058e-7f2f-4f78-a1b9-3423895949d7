<template>
  <article>
    <div
      :class="[
        'sticky top-0 z-10 flex h-12 shrink-0 items-center justify-between transition-colors duration-300',
        isDark ? 'bg-dark-primary' : 'bg-white'
      ]"
    >
      <h1
        :class="[
          'text-xl font-semibold transition-colors duration-300',
          isDark ? 'text-white' : 'text-gray-900'
        ]"
      >
        发布管理
      </h1>

      <div class="flex items-center gap-x-4">
        <Combobox as="div" v-model="activeApp">
          <div class="relative">
            <ComboboxInput
              :class="[
                'w-full rounded-md border-0 py-1.5 pl-3 pr-10 shadow-sm ring-1 ring-inset focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6 transition-colors duration-300',
                isDark
                  ? 'bg-dark-secondary text-dark-primary ring-dark-primary'
                  : 'bg-white text-gray-900 ring-gray-300'
              ]"
              :display-value="(app: any) => app?.app_name || ''"
              @change="query = $event.target.value"
              placeholder="搜索应用..."
            />
            <ComboboxButton
              class="absolute inset-y-0 right-0 flex items-center rounded-r-md px-2 focus:outline-none"
            >
              <ChevronDownIcon
                class="h-4 w-4"
                :class="[isDark ? 'text-gray-300' : 'text-gray-400']"
              />
            </ComboboxButton>

            <ComboboxOptions
              v-if="apps.length > 0"
              :class="[
                'fixed z-20 mt-1 max-h-60 w-60 overflow-auto rounded-md py-1 text-base shadow-lg ring-1 ring-opacity-5 focus:outline-none sm:text-sm transition-colors duration-300',
                isDark ? 'bg-dark-secondary ring-black' : 'bg-white ring-black'
              ]"
            >
              <div
                v-if="query !== '' && filteredApps.length === 0"
                :class="[
                  'relative cursor-default select-none py-2 px-4',
                  isDark ? 'text-gray-400' : 'text-gray-500'
                ]"
              >
                没有找到匹配的应用
              </div>
              <ComboboxOption
                v-for="app in filteredApps"
                :key="app.package_name"
                :value="app"
                as="template"
              >
                <li
                  :class="[
                    'relative cursor-default select-none py-2 pl-8 pr-4 transition-colors duration-300',
                    isSelectApp(app)
                      ? 'bg-blue-600 text-white'
                      : isDark
                      ? 'text-dark-primary hover:bg-dark-tertiary'
                      : 'text-gray-900 hover:bg-gray-100'
                  ]"
                >
                  <span
                    :class="['block truncate', isSelectApp(app) && 'font-semibold']"
                  >
                    {{ app.app_name }}
                  </span>

                  <span
                    v-if="isSelectApp(app)"
                    :class="[
                      'absolute inset-y-0 left-0 flex items-center pl-1.5',
                      isCompleteApp(activeApp) && activeApp.package_name == app.package_name
                        ? 'text-white'
                        : 'text-blue-600'
                    ]"
                  >
                  </span>
                </li>
              </ComboboxOption>
            </ComboboxOptions>
          </div>
        </Combobox>
        <button
          type="button"
          :class="[
            'block rounded-md px-2 py-1.5 text-center text-sm font-medium transition-all duration-200 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 flex items-center gap-x-2',
            isDark
              ? 'border border-blue-500 text-blue-500 hover:bg-blue-500/10 focus-visible:outline-blue-500'
              : 'border border-blue-600 text-blue-600 hover:bg-blue-600/10 focus-visible:outline-blue-600'
          ]"
          @click="showCreateDialog"
          aria-label="创建新发布"
        >
          <Plus
            class="h-4 w-4"
            :class="[isDark ? 'text-blue-500' : 'text-blue-600']"
          />
          新增发布
        </button>
      </div>
    </div>

    <div ref="ak_table" class="py-4">
      <!-- 加载时显示 Shimmer 效果 -->
      <ShimmerLoading
        v-if="isPublishesLoading"
        type="table"
        :rows="6"
        :columns="8"
        :column-widths="[
          '120px',
          '60px',
          '80px',
          '60px',
          '60px',
          '60px',
          '60px',
          '160px'
        ]"
        container-class="mb-4"
      />

      <!-- 数据表格 -->
      <a-table
        v-else
        :bordered="false"
        :data="data"
        :pagination="true"
        page-position="bottom"
        :scroll="scroll"
        :scroll-x="true"
      >
        <template #columns>
          <a-table-column
            :ellipsis="true"
            :width="120"
            title="版本名称"
            data-index="version_name"
            :tooltip="true"
          >
            <template #cell="{ record }">
              <div class="flex items-center">
                <span class="font-medium">{{ record.version_name }}</span>
                <span class="ml-2 text-gray-500">({{ record.version_code }})</span>
              </div>
            </template>
          </a-table-column>
          <a-table-column
            :ellipsis="true"
            :width="60"
            title="状态"
            data-index="active"
          >
            <template #cell="{ record }">
              <a-tag bordered :color="record.active === 1 ? 'green' : 'gray'">
                {{ record.active === 1 ? '已激活' : '未激活' }}
              </a-tag>
            </template>
          </a-table-column>
          <a-table-column
            :ellipsis="true"
            :width="80"
            title="更新类型"
            data-index="update_type"
          >
            <template #cell="{ record }">
              <a-tag
                bordered
                :color="record.update_type === 'force' ? 'red' : 'blue'"
              >
                {{ record.update_type === 'force' ? '强制更新' : '普通更新' }}
              </a-tag>
            </template>
          </a-table-column>
          <a-table-column
            :ellipsis="true"
            :width="60"
            title="渠道"
            data-index="channel"
            :tooltip="true"
          ></a-table-column>
          <a-table-column
            :ellipsis="true"
            :width="60"
            title="平台"
            data-index="platform"
            :tooltip="true"
          ></a-table-column>
          <a-table-column
            :ellipsis="true"
            :width="60"
            title="发布时间"
            data-index="publish_time"
            :tooltip="true"
            :sortable="{
              sortDirections: ['descend', 'ascend'],
              defaultSortOrder: 'descend'
            }"
          ></a-table-column>
          <a-table-column
            :ellipsis="true"
            :width="60"
            title="创建时间"
            data-index="create_time"
            :tooltip="true"
          ></a-table-column>
          <a-table-column :width="160" title="操作">
            <template #cell="{ record }">
              <a-space>
                <a-button
                  size="mini"
                  :class="[
                    'transition-colors duration-200',
                    isDark
                      ? record.active === 1
                        ? 'bg-green-900/60 text-green-100 hover:bg-green-800/70'
                        : 'bg-gray-700 text-gray-100 hover:bg-gray-600'
                      : ''
                  ]"
                  :status="record.active === 1 ? 'success' : 'normal'"
                  @click="handleClick(record)"
                >
                  {{ record.active === 1 ? '已激活' : '激活' }}
                </a-button>
                <a-button
                  size="mini"
                  :class="[
                    'transition-colors duration-200',
                    isDark ? 'bg-gray-700 text-gray-100 hover:bg-gray-600' : ''
                  ]"
                  @click="handleEdit(record)"
                >
                  编辑
                </a-button>
                <a-button
                  size="mini"
                  status="danger"
                  :class="[
                    'transition-colors duration-200',
                    isDark ? 'bg-red-900/60 text-red-100 hover:bg-red-800/70' : ''
                  ]"
                  @click="handleDelete(record)"
                >
                  删除
                </a-button>
              </a-space>
            </template>
          </a-table-column>
        </template>
      </a-table>
    </div>
    <a-modal
      v-model:visible="configCreateVisible"
      @cancel="cancelCreateConfig"
      @before-ok="createConfig"
    >
      <template #title>
        <div class="flex items-center gap-2">
          <span>更新发布状态</span>
          <a-tag
            :color="configCreateForm.active === '1' ? 'green' : 'gray'"
            class="ml-2"
          >
            {{ configCreateForm.active === '1' ? '已激活' : '未激活' }}
          </a-tag>
        </div>
      </template>
      <div class="py-2">
        <a-form ref="createFormRef" :model="configCreateForm">
          <a-form-item field="active" label="激活状态">
            <a-radio-group v-model="configCreateForm.active" type="button">
              <a-radio value="1">
                <template #icon>
                  <icon-check-circle-fill v-if="configCreateForm.active === '1'" />
                </template>
                激活
              </a-radio>
              <a-radio value="0">
                <template #icon>
                  <icon-minus-circle-fill v-if="configCreateForm.active === '0'" />
                </template>
                未激活
              </a-radio>
            </a-radio-group>
          </a-form-item>
          <div class="text-gray-500 text-sm mt-2">
            <p>激活状态说明：</p>
            <ul class="list-disc pl-4 mt-1">
              <li>激活：该版本将被推送给用户进行更新</li>
              <li>未激活：该版本不会推送给用户</li>
            </ul>
          </div>
        </a-form>
      </div>
    </a-modal>
    <DeleteConfirmDialog
      v-model:visible="deleteVisible"
      :title="'删除版本确认'"
      :content="deleteWarning"
      @confirm="handleDeleteConfirm"
      @cancel="handleDeleteCancel"
    />
    <a-modal
      v-model:visible="createPublishVisible"
      @cancel="cancelCreatePublish"
      @before-ok="submitCreatePublish"
    >
      <template #title>创建发布</template>
      <div>
        <a-form ref="createPublishFormRef" :model="createPublishForm">
          <a-form-item
            field="version_code"
            label="版本号"
            :rules="[{ required: true, message: '请输入版本号' }]"
          >
            <a-input-number
              v-model="createPublishForm.version_code"
              placeholder="请输入版本号"
            />
          </a-form-item>
          <a-form-item
            field="version_name"
            label="版本名称"
            :rules="[{ required: true, message: '请输入版本名称' }]"
          >
            <a-input
              v-model="createPublishForm.version_name"
              placeholder="请输入版本名称"
            />
          </a-form-item>
          <a-form-item
            field="channel"
            label="渠道"
            :rules="[{ required: true, message: '请输入渠道' }]"
          >
            <a-input v-model="createPublishForm.channel" placeholder="请输入渠道" />
          </a-form-item>
          <a-form-item
            field="update_type"
            label="更新类型"
            :rules="[{ required: true, message: '请选择更新类型' }]"
          >
            <a-select
              v-model="createPublishForm.update_type"
              placeholder="请选择更新类型"
            >
              <a-option value="force">强制更新</a-option>
              <a-option value="normal">普通更新</a-option>
            </a-select>
          </a-form-item>
          <a-form-item field="platform" label="平台">
            <a-input v-model="createPublishForm.platform" disabled />
          </a-form-item>
          <a-form-item
            field="upgrade_features"
            label="更新特性"
            :rules="[{ required: true, message: '请输入更新特性' }]"
          >
            <a-textarea
              v-model="createPublishForm.upgrade_features"
              placeholder="请输入更新特性"
            />
          </a-form-item>
          <a-form-item
            field="package_downloadUrl"
            label="应用包文件"
            :rules="[{ required: true, message: '请上传应用包文件' }]"
          >
            <FileUpload
              v-model="createPublishForm.package_downloadUrl"
              :accept="getAcceptedFileTypes(createPublishForm.platform)"
              :max-size="500 * 1024 * 1024"
              @upload-success="handleUploadSuccess"
              @upload-error="handleUploadError"
            />
          </a-form-item>
          <a-form-item
            field="official_site"
            label="官网地址"
            :rules="[{ required: true, message: '请输入官网地址' }]"
          >
            <a-input
              v-model="createPublishForm.official_site"
              placeholder="请输入官网地址"
            />
          </a-form-item>
        </a-form>
      </div>
    </a-modal>
    <a-modal
      v-model:visible="editPublishVisible"
      @cancel="cancelEditPublish"
      @before-ok="submitEditPublish"
    >
      <template #title>编辑发布</template>
      <div>
        <a-form ref="editPublishFormRef" :model="editPublishForm">
          <a-form-item
            field="version_code"
            label="版本号"
            :rules="[{ required: true, message: '请输入版本号' }]"
          >
            <a-input-number
              v-model="editPublishForm.version_code"
              placeholder="请输入版本号"
            />
          </a-form-item>
          <a-form-item
            field="version_name"
            label="版本名称"
            :rules="[{ required: true, message: '请输入版本名称' }]"
          >
            <a-input
              v-model="editPublishForm.version_name"
              placeholder="请输入版本名称"
            />
          </a-form-item>
          <a-form-item
            field="channel"
            label="渠道"
            :rules="[{ required: true, message: '请输入渠道' }]"
          >
            <a-input v-model="editPublishForm.channel" placeholder="请输入渠道" />
          </a-form-item>
          <a-form-item
            field="update_type"
            label="更新类型"
            :rules="[{ required: true, message: '请选择更新类型' }]"
          >
            <a-select
              v-model="editPublishForm.update_type"
              placeholder="请选择更新类型"
            >
              <a-option value="force">强制更新</a-option>
              <a-option value="normal">普通更新</a-option>
            </a-select>
          </a-form-item>
          <a-form-item field="platform" label="平台">
            <a-input v-model="editPublishForm.platform" disabled />
          </a-form-item>
          <a-form-item
            field="upgrade_features"
            label="更新特性"
            :rules="[{ required: true, message: '请输入更新特性' }]"
          >
            <a-textarea
              v-model="editPublishForm.upgrade_features"
              placeholder="请输入更新特性"
            />
          </a-form-item>
          <a-form-item
            field="package_downloadUrl"
            label="应用包文件"
            :rules="[{ required: true, message: '请上传应用包文件' }]"
          >
            <FileUpload
              v-model="editPublishForm.package_downloadUrl"
              :accept="getAcceptedFileTypes(editPublishForm.platform)"
              :max-size="500 * 1024 * 1024"
              @upload-success="handleEditUploadSuccess"
              @upload-error="handleEditUploadError"
            />
          </a-form-item>
          <a-form-item
            field="official_site"
            label="官网地址"
            :rules="[{ required: true, message: '请输入官网地址' }]"
          >
            <a-input
              v-model="editPublishForm.official_site"
              placeholder="请输入官网地址"
            />
          </a-form-item>
        </a-form>
      </div>
    </a-modal>
  </article>
</template>

<script setup lang="ts">
import { useAppsStore } from '@/stores/appsStore'
import { storeToRefs } from 'pinia'
import {
  Combobox,
  ComboboxButton,
  ComboboxInput,
  ComboboxOption,
  ComboboxOptions
} from '@headlessui/vue'
import { appsApi } from '@/networking/api'
import { ChevronDownIcon } from '@heroicons/vue/20/solid'
import { onMounted, ref, watch, computed } from 'vue'
import { useWindowSize } from '@vueuse/core'
import moment from 'dayjs'
import { isArray } from 'lodash'
import { Plus } from 'lucide-vue-next'
import {
  IconCheckCircleFill,
  IconMinusCircleFill
} from '@arco-design/web-vue/es/icon'
import FileUpload from '@/components/FileUpload.vue'
import type { UploadResult } from '@/utils/fileUpload'
import DeleteConfirmDialog from '@/components/DeleteConfirmDialog.vue'
import ShimmerLoading from '@/components/ShimmerLoading.vue'
import { useThemeStore } from '@/stores/themeStore'
import { useLoading } from '@/composables/useLoading'
import { Message } from '@arco-design/web-vue'
import type { App } from '@/types/api'

const data = ref<any[]>([])
const store = useAppsStore()
const { activeApp, apps } = storeToRefs(store)

// 类型守卫函数，检查activeApp是否是完整的App对象
function isCompleteApp(app: App | { app_name: string }): app is App {
  return 'package_name' in app && 'platform' in app
}

// 主题管理
const themeStore = useThemeStore()
const { isDark } = storeToRefs(themeStore)

// 加载状态管理
const { isLoading, withLoading } = useLoading([
  'publishes',
  'activate',
  'delete',
  'create',
  'edit'
])
const isPublishesLoading = isLoading('publishes')

// 搜索查询
const query = ref('')

// 过滤应用列表
const filteredApps = computed(() => {
  if (query.value === '') {
    return apps.value
  }
  return apps.value.filter((app: any) =>
    app.app_name.toLowerCase().includes(query.value.toLowerCase())
  )
})

const { height: windowHeight } = useWindowSize()
const ak_table = ref(null as any)
const scroll = ref({
  y: 500
})

watch(windowHeight, () => {
  scroll.value.y = Math.max(windowHeight.value - 165, 300)
})

onMounted(async () => {
  scroll.value.y = Math.max(windowHeight.value - 165, 300)
  try {
    await store.reloadApps()
    await reloadUploadPackages()
  } catch (e) {
    console.log(e)
  }
})

function isSelectApp(app: any) {
  return (
    isCompleteApp(activeApp.value) &&
    activeApp.value.package_name === app.package_name &&
    activeApp.value.platform === app.platform
  )
}

async function reloadUploadPackages() {
  if (activeApp.value && isCompleteApp(activeApp.value)) {
    await withLoading(
      'publishes',
      async () => {
        const app = activeApp.value as App
        const res = await appsApi.allPublish({
          package_name: app.package_name,
          platform: app.platform
        })
        console.log('reloadUploadPackages res', res)
        if (isArray(res.data)) {
          data.value = res.data.map((item: any) => {
            return {
              ...item,
              create_time: moment(item.create_time).format('YYYY-MM-DD HH:mm:ss'),
              publish_time: moment(item.publish_time).format('YYYY-MM-DD HH:mm:ss')
            }
          })
        } else {
          data.value = []
        }
      },
      {
        onError: (error) => {
          console.error('加载发布列表失败:', error)
          Message.error('加载发布列表失败')
        }
      }
    )
  }
}

watch(
  () => activeApp.value,
  (val) => {
    console.log('activeApp', val)
    store.setActiveApp(val)
    reloadUploadPackages()
    // 选择应用后清空搜索查询
    query.value = ''
  }
)

function handleClick(record: any) {
  configCreateForm.value.publih_id = record._id
  configCreateForm.value.active = record.active + ''
  configCreateVisible.value = true
}

const createFormRef = ref()
const configCreateVisible = ref(false)

const configCreateForm = ref({
  publih_id: '',
  active: '0'
})



const cancelCreateConfig = () => {
  configCreateVisible.value = false
  configCreateForm.value = {
    publih_id: '',
    active: '0'
  }
}

const createConfig = async (done: (success: boolean) => boolean | void) => {
  try {
    await appsApi.activePublish({
      publih_id: configCreateForm.value.publih_id,
      active: configCreateForm.value.active
    })

    await reloadUploadPackages()
    configCreateForm.value = {
      publih_id: '',
      active: '0'
    }
    done(true)
    return
  } catch (e) {
    console.error('Activate publish failed:', e)
    done(false)
    return
  }
}

const createPublishVisible = ref(false)
const createPublishFormRef = ref()
const createPublishForm = ref({
  version_code: undefined as number | undefined,
  version_name: '',
  channel: '',
  update_type: 'normal',
  upgrade_features: '',
  platform: '',
  package_downloadUrl: '',
  official_site: ''
})

const showCreateDialog = () => {
  if (!activeApp.value) {
    throw new Error('请先选择应用')
  }
  if (!isCompleteApp(activeApp.value)) {
    throw new Error('请先选择完整的应用信息')
  }
  createPublishForm.value.platform = activeApp.value.platform
  createPublishVisible.value = true
}

const cancelCreatePublish = () => {
  createPublishVisible.value = false
  createPublishForm.value = {
    version_code: undefined,
    version_name: '',
    channel: '',
    update_type: 'normal',
    upgrade_features: '',
    platform: '',
    package_downloadUrl: '',
    official_site: ''
  }
}

const submitCreatePublish = async (done: (success: boolean) => boolean | void) => {
  try {
    if (!activeApp.value) {
      throw new Error('请先选择应用')
    }

    if (!createPublishForm.value.version_code) {
      throw new Error('请输入版本号')
    }

    if (!isCompleteApp(activeApp.value)) {
      throw new Error('请先选择完整的应用信息')
    }

    await appsApi.createPublish({
      package_name: activeApp.value.package_name,
      platform: activeApp.value.platform,
      channel: createPublishForm.value.channel,
      update_type: createPublishForm.value.update_type === 'force' ? 1 : 0,
      version_name: createPublishForm.value.version_name,
      version_code: String(createPublishForm.value.version_code),
      upgrade_features: createPublishForm.value.upgrade_features,
      package_downloadUrl: createPublishForm.value.package_downloadUrl,
      official_site: createPublishForm.value.official_site,
      locale: 'zh-CN',
      active: -1,
      min_binary_version_code: '0',
      ext: ''
    })

    await reloadUploadPackages()
    createPublishVisible.value = false
    createPublishForm.value = {
      version_code: undefined,
      version_name: '',
      channel: '',
      update_type: 'normal',
      upgrade_features: '',
      platform: '',
      package_downloadUrl: '',
      official_site: ''
    }
    done(true)
  } catch (e) {
    console.error('Create publish failed:', e)
    done(false)
  }
}

const handleDelete = async (record: any) => {
  deleteVisible.value = true
  deleteWarning.value = `确定要删除版本 ${record.version_name} (${record.version_code}) 吗？`
  deleteRecord.value = record
}

const deleteVisible = ref(false)
const deleteWarning = ref('')
const deleteRecord = ref<any>(null)

const handleDeleteConfirm = async () => {
  if (!deleteRecord.value) return
  try {
    await appsApi.deletePublish(deleteRecord.value._id)
    await reloadUploadPackages()
    deleteVisible.value = false
  } catch (e) {
    console.error('Delete publish failed:', e)
  }
}

const handleDeleteCancel = () => {
  deleteVisible.value = false
}

const editPublishVisible = ref(false)
const editPublishFormRef = ref()
const editPublishForm = ref({
  _id: '',
  version_code: undefined as number | undefined,
  version_name: '',
  channel: '',
  update_type: 'normal',
  upgrade_features: '',
  platform: '',
  package_downloadUrl: '',
  official_site: ''
})

const handleEdit = (record: any) => {
  editPublishForm.value = {
    _id: record._id,
    version_code: Number(record.version_code),
    version_name: record.version_name,
    channel: record.channel,
    update_type: record.update_type === 1 ? 'force' : 'normal',
    upgrade_features: record.upgrade_features,
    platform: record.platform,
    package_downloadUrl: record.package_downloadUrl,
    official_site: record.official_site
  }
  editPublishVisible.value = true
}

const cancelEditPublish = () => {
  editPublishVisible.value = false
  editPublishForm.value = {
    _id: '',
    version_code: undefined,
    version_name: '',
    channel: '',
    update_type: 'normal',
    upgrade_features: '',
    platform: '',
    package_downloadUrl: '',
    official_site: ''
  }
}

const submitEditPublish = async (done: (success: boolean) => boolean | void) => {
  try {
    if (!activeApp.value) {
      throw new Error('请先选择应用')
    }

    if (!editPublishForm.value.version_code) {
      throw new Error('请输入版本号')
    }

    if (!isCompleteApp(activeApp.value)) {
      throw new Error('请先选择完整的应用信息')
    }

    await appsApi.updatePublish({
      publish_id: editPublishForm.value._id,
      package_name: activeApp.value.package_name,
      platform: activeApp.value.platform,
      channel: editPublishForm.value.channel,
      update_type: editPublishForm.value.update_type === 'force' ? 1 : 0,
      version_name: editPublishForm.value.version_name,
      version_code: String(editPublishForm.value.version_code),
      upgrade_features: editPublishForm.value.upgrade_features,
      package_downloadUrl: editPublishForm.value.package_downloadUrl,
      official_site: editPublishForm.value.official_site,
      locale: 'zh-CN',
      active: -1,
      min_binary_version_code: '0',
      ext: ''
    })

    await reloadUploadPackages()
    editPublishVisible.value = false
    editPublishForm.value = {
      _id: '',
      version_code: undefined,
      version_name: '',
      channel: '',
      update_type: 'normal',
      upgrade_features: '',
      platform: '',
      package_downloadUrl: '',
      official_site: ''
    }
    done(true)
  } catch (e) {
    console.error('Update publish failed:', e)
    done(false)
  }
}

// 根据平台获取支持的文件类型
const getAcceptedFileTypes = (platform: string): string => {
  switch (platform) {
    case 'android':
      return '.apk'
    case 'mac':
    case 'macarm':
      return '.dmg'
    case 'windows':
      return '.exe'
    default:
      return '.apk,.dmg,.exe'
  }
}

// 文件上传处理函数
const handleUploadSuccess = (result: UploadResult) => {
  createPublishForm.value.package_downloadUrl = result.downloadUrl
  console.log('Create form upload success:', result)
  // 手动触发表单验证，清除错误状态
  createPublishFormRef.value?.clearValidate('package_downloadUrl')
  Message.success('文件上传成功')
}

const handleUploadError = (error: Error) => {
  console.error('Create form upload error:', error)
  createPublishForm.value.package_downloadUrl = ''
  Message.error(`上传失败: ${error.message}`)
}

const handleEditUploadSuccess = (result: UploadResult) => {
  editPublishForm.value.package_downloadUrl = result.downloadUrl
  console.log('Edit form upload success:', result)
  // 手动触发表单验证，清除错误状态
  editPublishFormRef.value?.clearValidate('package_downloadUrl')
  Message.success('文件上传成功')
}

const handleEditUploadError = (error: Error) => {
  console.error('Edit form upload error:', error)
  editPublishForm.value.package_downloadUrl = ''
  Message.error(`上传失败: ${error.message}`)
}
</script>
<style scoped lang="less">
@import './index.less';
</style>
