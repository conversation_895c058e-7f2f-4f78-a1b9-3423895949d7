import { appsApi } from '@/networking/api'
import * as tus from 'tus-js-client'

export interface FileUploadOptions {
  fileName: string
  mimeType?: string
  uploadSign?: string
  onProgress?: (progress: number) => void
  onComplete?: (fileId: string) => void
  onError?: (error: Error) => void
  chunkSize?: number // TUS 块大小，默认 5MB
  retryDelays?: number[] // 重试延迟配置
  userId?: string // 用户ID，用于 TUS metadata
}

export interface FileUploadClientHandler {
  (client: tus.Upload): void
}

export interface UploadResult {
  fileId: string
  downloadUrl: string
}

/**
 * 上传文件到存储中心（使用 TUS 协议）
 * @param file 要上传的文件
 * @param options 上传选项
 * @returns Promise<UploadResult>
 */
export async function uploadFile(
  file: File,
  options: FileUploadOptions
): Promise<UploadResult> {
  const {
    fileName,
    mimeType = file.type,
    uploadSign,
    onProgress,
    onComplete,
    onError,
    chunkSize = 5 * 1024 * 1024, // 默认 5MB
    retryDelays = [0, 3000, 5000, 10000, 20000], // 默认重试延迟
    userId = 'anonymous' // 默认用户ID
  } = options

  try {
    // 获取上传签名
    const sign = uploadSign || (await getUploadSign(fileName))
    const serverUrl = 'https://storage-center.basecastle.com/files/'

    console.log('uploadFile serverUrl:', serverUrl, 'sign:', sign)

    // 使用 TUS 协议上传文件
    return new Promise((resolve, reject) => {
      let upload: tus.Upload

      upload = new tus.Upload(file, {
        endpoint: serverUrl,
        retryDelays: retryDelays,
        chunkSize: chunkSize,
        removeFingerprintOnSuccess: true, // 上传成功后移除指纹，避免重复上传
        metadata: {
          filename: fileName,
          filetype: mimeType || '',
          uid: userId,
        },
        headers: {
          token: sign,
        },
        onError: (error) => {
          console.error('TUS upload failed:', error)
          const uploadError = new Error(`上传失败: ${error.message}`)
          onError?.(uploadError)
          reject(uploadError)
        },
        onProgress: (bytesUploaded, bytesTotal) => {
          const progress = Math.round((bytesUploaded / bytesTotal) * 100)
          onProgress?.(progress)
          console.log(`TUS upload progress: ${progress}% (${bytesUploaded}/${bytesTotal})`)
        },
        onSuccess: function() {
          console.log('TUS upload completed successfully')
          const uploadUrl = upload.url
          if (uploadUrl) {
            // 从上传 URL 中提取文件 ID
            const fileId = uploadUrl.replace(serverUrl, '')
            const downloadUrl = uploadUrl

            console.log('uploadFile Complete! fileId:', fileId, 'downloadUrl:', downloadUrl)
            onComplete?.(fileId)

            resolve({
              fileId,
              downloadUrl
            })
          } else {
            const noUrlError = new Error('上传完成但未获取到文件URL')
            onError?.(noUrlError)
            reject(noUrlError)
          }
        }
      })

      // 开始上传
      upload.start()
    })
  } catch (error) {
    console.error('uploadFile failed:', error)
    onError?.(error as Error)
    throw error
  }
}

/**
 * 获取上传签名
 * @param fileName 文件名
 * @returns Promise<string>
 */
async function getUploadSign(fileName: string): Promise<string> {
  try {
    const response = await appsApi.storageSign({ file_name: fileName })
    return response.data.token || response.data.sign || response.data
  } catch (error) {
    console.error('获取上传签名失败:', error)
    throw new Error('获取上传签名失败')
  }
}



/**
 * 验证文件类型
 * @param file 文件对象
 * @param allowedTypes 允许的文件类型数组（支持 MIME 类型和文件扩展名）
 * @returns boolean
 */
export function validateFileType(file: File, allowedTypes: string[]): boolean {
  return allowedTypes.some(type => {
    // 处理通配符 MIME 类型，如 image/*
    if (type.endsWith('/*')) {
      return file.type.startsWith(type.slice(0, -1))
    }

    // 处理文件扩展名，如 .apk, .ipa
    if (type.startsWith('.')) {
      const fileName = file.name.toLowerCase()
      const extension = type.toLowerCase()
      return fileName.endsWith(extension)
    }

    // 处理完整的 MIME 类型
    return file.type === type
  })
}

/**
 * 验证文件大小
 * @param file 文件对象
 * @param maxSize 最大文件大小（字节）
 * @returns boolean
 */
export function validateFileSize(file: File, maxSize: number): boolean {
  return file.size <= maxSize
}

/**
 * 格式化文件大小
 * @param bytes 字节数
 * @returns string
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'

  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * TUS 上传管理器类
 * 提供暂停、恢复、取消等功能
 */
export class TusUploadManager {
  private upload: tus.Upload | null = null
  private isUploading = false
  private isPaused = false

  constructor(
    private file: File,
    private options: FileUploadOptions,
    private serverUrl: string,
    private sign: string
  ) {}

  /**
   * 开始上传
   */
  async start(): Promise<UploadResult> {
    if (this.isUploading) {
      throw new Error('上传已在进行中')
    }

    const {
      fileName,
      mimeType = this.file.type,
      onProgress,
      onComplete,
      onError,
      chunkSize = 5 * 1024 * 1024,
      retryDelays = [0, 3000, 5000, 10000, 20000],
      userId = 'anonymous'
    } = this.options

    return new Promise((resolve, reject) => {
      this.upload = new tus.Upload(this.file, {
        endpoint: this.serverUrl,
        retryDelays: retryDelays,
        chunkSize: chunkSize,
        removeFingerprintOnSuccess: true,
        metadata: {
          filename: fileName,
          filetype: mimeType || '',
          uid: userId,
        },
        headers: {
          token: this.sign,
        },
        onError: (error) => {
          console.error('TUS upload failed:', error)
          this.isUploading = false
          const uploadError = new Error(`上传失败: ${error.message}`)
          onError?.(uploadError)
          reject(uploadError)
        },
        onProgress: (bytesUploaded, bytesTotal) => {
          const progress = Math.round((bytesUploaded / bytesTotal) * 100)
          onProgress?.(progress)
          console.log(`TUS upload progress: ${progress}% (${bytesUploaded}/${bytesTotal})`)
        },
        onSuccess: () => {
          console.log('TUS upload completed successfully')
          this.isUploading = false
          const uploadUrl = this.upload?.url
          if (uploadUrl) {
            const fileId = uploadUrl.replace(this.serverUrl, '')
            const downloadUrl = uploadUrl

            console.log('uploadFile Complete! fileId:', fileId, 'downloadUrl:', downloadUrl)
            onComplete?.(fileId)

            resolve({
              fileId,
              downloadUrl
            })
          } else {
            const noUrlError = new Error('上传完成但未获取到文件URL')
            onError?.(noUrlError)
            reject(noUrlError)
          }
        }
      })

      this.isUploading = true
      this.upload.start()
    })
  }

  /**
   * 暂停上传
   */
  pause(): void {
    if (this.upload && this.isUploading && !this.isPaused) {
      this.upload.abort()
      this.isPaused = true
      console.log('Upload paused')
    }
  }

  /**
   * 恢复上传
   */
  resume(): void {
    if (this.upload && this.isPaused) {
      this.upload.start()
      this.isPaused = false
      console.log('Upload resumed')
    }
  }

  /**
   * 取消上传
   */
  cancel(): void {
    if (this.upload) {
      this.upload.abort()
      this.isUploading = false
      this.isPaused = false
      console.log('Upload cancelled')
    }
  }

  /**
   * 获取上传状态
   */
  getStatus(): { isUploading: boolean; isPaused: boolean } {
    return {
      isUploading: this.isUploading,
      isPaused: this.isPaused
    }
  }
}
