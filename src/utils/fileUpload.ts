import { appsApi } from '@/networking/api'

export interface FileUploadOptions {
  fileName: string
  mimeType?: string
  uploadSign?: string
  onProgress?: (progress: number) => void
  onComplete?: (fileId: string) => void
  onError?: (error: Error) => void
}

export interface UploadResult {
  fileId: string
  downloadUrl: string
}

/**
 * 上传文件到存储中心
 * @param file 要上传的文件
 * @param options 上传选项
 * @returns Promise<UploadResult>
 */
export async function uploadFile(
  file: File,
  options: FileUploadOptions
): Promise<UploadResult> {
  const {
    fileName,
    mimeType = file.type,
    uploadSign,
    onProgress,
    onComplete,
    onError
  } = options

  try {
    // 获取上传签名
    const sign = uploadSign || (await getUploadSign(fileName))
    const serverUrl = 'https://storage-center.basecastle.com/files/'

    console.log('uploadFile serverUrl:', serverUrl, 'sign:', sign)

    // 创建 FormData
    const formData = new FormData()
    formData.append('file', file, fileName)
    formData.append('filename', fileName)
    formData.append('filetype', mimeType)

    // 使用 XMLHttpRequest 进行上传以支持进度回调
    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest()

      // 监听上传进度
      xhr.upload.addEventListener('progress', (event) => {
        if (event.lengthComputable) {
          const progress = Math.round((event.loaded / event.total) * 100)
          onProgress?.(progress)
          console.log(`uploadFile progress: ${progress}%`)
        }
      })

      // 监听请求完成
      xhr.addEventListener('load', () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          try {
            const response = JSON.parse(xhr.responseText)
            const fileId = response.fileId || response.id || ''
            const downloadUrl = `${serverUrl}${fileId}`
            
            console.log('uploadFile Complete! fileId:', fileId)
            onComplete?.(fileId)
            
            resolve({
              fileId,
              downloadUrl
            })
          } catch (error) {
            const parseError = new Error('解析响应失败')
            onError?.(parseError)
            reject(parseError)
          }
        } else {
          const statusError = new Error(`上传失败: ${xhr.status} ${xhr.statusText}`)
          onError?.(statusError)
          reject(statusError)
        }
      })

      // 监听请求错误
      xhr.addEventListener('error', () => {
        const networkError = new Error('网络错误')
        onError?.(networkError)
        reject(networkError)
      })

      // 监听请求超时
      xhr.addEventListener('timeout', () => {
        const timeoutError = new Error('上传超时')
        onError?.(timeoutError)
        reject(timeoutError)
      })

      // 配置请求
      xhr.open('POST', serverUrl)
      xhr.setRequestHeader('token', sign)
      xhr.timeout = 300000 // 5分钟超时

      // 发送请求
      xhr.send(formData)
    })
  } catch (error) {
    console.error('uploadFile failed:', error)
    onError?.(error as Error)
    throw error
  }
}

/**
 * 获取上传签名
 * @param fileName 文件名
 * @returns Promise<string>
 */
async function getUploadSign(fileName: string): Promise<string> {
  try {
    const response = await appsApi.storageSign({ file_name: fileName })
    return response.data.token || response.data.sign || response.data
  } catch (error) {
    console.error('获取上传签名失败:', error)
    throw new Error('获取上传签名失败')
  }
}

/**
 * 验证文件类型
 * @param file 文件对象
 * @param allowedTypes 允许的文件类型数组（支持 MIME 类型和文件扩展名）
 * @returns boolean
 */
export function validateFileType(file: File, allowedTypes: string[]): boolean {
  console.log('validateFileType:', {
    fileName: file.name,
    fileType: file.type,
    allowedTypes: allowedTypes
  })

  const isValid = allowedTypes.some(type => {
    // 处理通配符 MIME 类型，如 image/*
    if (type.endsWith('/*')) {
      const result = file.type.startsWith(type.slice(0, -1))
      console.log(`Checking wildcard MIME type ${type}: ${result}`)
      return result
    }

    // 处理文件扩展名，如 .apk, .ipa
    if (type.startsWith('.')) {
      const fileName = file.name.toLowerCase()
      const extension = type.toLowerCase()
      const result = fileName.endsWith(extension)
      console.log(`Checking extension ${type}: fileName=${fileName}, extension=${extension}, result=${result}`)
      return result
    }

    // 处理完整的 MIME 类型
    const result = file.type === type
    console.log(`Checking MIME type ${type}: ${result}`)
    return result
  })

  console.log('validateFileType result:', isValid)
  return isValid
}

/**
 * 验证文件大小
 * @param file 文件对象
 * @param maxSize 最大文件大小（字节）
 * @returns boolean
 */
export function validateFileSize(file: File, maxSize: number): boolean {
  return file.size <= maxSize
}

/**
 * 格式化文件大小
 * @param bytes 字节数
 * @returns string
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
